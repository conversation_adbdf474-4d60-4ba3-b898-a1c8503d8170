#!/usr/bin/env python3
"""
Queue System Test Suite
Tests background job processing, email queueing, security event processing, data cleanup jobs, and queue statistics
"""

import requests
import json
import time
import sys
import random

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def print_header(title):
    """Print formatted test header"""
    print(f"\n{'='*60}")
    print(f"⚡ {title}")
    print(f"{'='*60}")

def print_test(test_name):
    """Print test name"""
    print(f"\n🧪 Testing: {test_name}")
    print("-" * 40)

def create_test_user():
    """Create a test user and return token"""
    unique_id = random.randint(10000, 99999)
    register_data = {
        "email": f"queue_test_{unique_id}@example.com",
        "password": "SecurePass123!",
        "firstName": "Queue",
        "lastName": "Test"
    }
    
    try:
        # Wait to avoid rate limiting
        time.sleep(5)
        response = requests.post(f"{API_BASE}/auth/register", json=register_data)
        if response.status_code == 201:
            return response.json().get('token'), register_data['email']
        elif response.status_code == 429:
            print("⚠️ Rate limited - waiting 120 seconds...")
            time.sleep(120)
            response = requests.post(f"{API_BASE}/auth/register", json=register_data)
            if response.status_code == 201:
                return response.json().get('token'), register_data['email']
        return None, None
    except Exception as e:
        print(f"❌ Failed to create test user: {str(e)}")
        return None, None

def test_queue_stats():
    """Test queue statistics endpoint"""
    print_test("Queue Statistics")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for queue stats")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        time.sleep(2)
        response = requests.get(f"{API_BASE}/queue/stats", headers=headers)
        print(f"Queue Stats Status: {response.status_code}")
        
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ Queue Stats Response: {json.dumps(stats_data, indent=2)}")
            return True
        else:
            print(f"❌ Queue Stats Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Queue Stats Test Error: {str(e)}")
        return False

def test_queue_test_email():
    """Test queue test email functionality"""
    print_test("Queue Test Email")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for queue test email")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        time.sleep(3)
        response = requests.post(f"{API_BASE}/queue/test-email", headers=headers)
        print(f"Queue Test Email Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            email_data = response.json()
            print(f"✅ Queue Test Email Response: {json.dumps(email_data, indent=2)}")
            return True
        else:
            print(f"❌ Queue Test Email Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Queue Test Email Test Error: {str(e)}")
        return False

def test_queue_cleanup():
    """Test queue data cleanup functionality"""
    print_test("Queue Data Cleanup")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for queue cleanup")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        time.sleep(3)
        response = requests.post(f"{API_BASE}/queue/cleanup", headers=headers)
        print(f"Queue Cleanup Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            cleanup_data = response.json()
            print(f"✅ Queue Cleanup Response: {json.dumps(cleanup_data, indent=2)}")
            return True
        else:
            print(f"❌ Queue Cleanup Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Queue Cleanup Test Error: {str(e)}")
        return False

def test_queue_security_events():
    """Test queue security event processing"""
    print_test("Queue Security Event Processing")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for queue security events")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        time.sleep(3)
        response = requests.post(f"{API_BASE}/queue/process-security-events", headers=headers)
        print(f"Queue Security Events Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            security_data = response.json()
            print(f"✅ Queue Security Events Response: {json.dumps(security_data, indent=2)}")
            return True
        else:
            print(f"❌ Queue Security Events Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Queue Security Events Test Error: {str(e)}")
        return False

def test_queue_integration():
    """Test queue system integration by triggering multiple jobs"""
    print_test("Queue System Integration")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for queue integration")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    results = {}
    
    # Test multiple queue operations in sequence
    queue_operations = [
        ("stats", f"{API_BASE}/queue/stats", "GET"),
        ("test-email", f"{API_BASE}/queue/test-email", "POST"),
        ("stats-after", f"{API_BASE}/queue/stats", "GET")
    ]
    
    for operation_name, url, method in queue_operations:
        try:
            time.sleep(2)  # Wait between operations
            
            if method == "GET":
                response = requests.get(url, headers=headers)
            else:
                response = requests.post(url, headers=headers)
            
            results[operation_name] = response.status_code in [200, 201]
            print(f"{operation_name}: {response.status_code} - {'✅' if results[operation_name] else '❌'}")
            
            if response.status_code in [200, 201]:
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)}")
                except:
                    print(f"  Response: {response.text}")
            else:
                print(f"  Error: {response.text}")
                
        except Exception as e:
            results[operation_name] = False
            print(f"❌ {operation_name} error: {str(e)}")
    
    # Check if queue stats show changes after operations
    success_rate = sum(1 for result in results.values() if result) / len(results)
    return success_rate >= 0.5

def run_queue_tests():
    """Run all queue system tests"""
    print_header("QUEUE SYSTEM TEST SUITE")
    
    # Wait for any rate limiting to reset
    print("⏳ Waiting 30 seconds for rate limiting to reset...")
    time.sleep(30)
    
    tests = [
        ("Queue Statistics", test_queue_stats),
        ("Queue Test Email", test_queue_test_email),
        ("Queue Data Cleanup", test_queue_cleanup),
        ("Queue Security Events", test_queue_security_events),
        ("Queue Integration", test_queue_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
            
            # Add delay between test suites
            time.sleep(10)
        except Exception as e:
            results[test_name] = False
            print(f"\n❌ FAILED: {test_name} - {str(e)}")
    
    # Print summary
    print_header("QUEUE TESTS SUMMARY")
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} test suites passed")
    
    if passed >= total * 0.6:  # 60% pass rate
        print("🎉 QUEUE SYSTEM TESTS MOSTLY SUCCESSFUL!")
        return True
    else:
        print("⚠️ Queue system needs attention")
        return False

if __name__ == "__main__":
    success = run_queue_tests()
    sys.exit(0 if success else 1)
