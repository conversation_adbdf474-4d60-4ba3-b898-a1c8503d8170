"""API Routes for Authentication, 2FA, and OAuth Endpoints with Rate Limiting"""

from masonite.routes import Route

ROUTES = [
    # CORS preflight OPTIONS routes for all endpoints
    Route.options('/*', 'Cors<PERSON>ontroller@preflight').name('api.cors.preflight'),

    # Public authentication endpoints with reasonable rate limiting
    Route.post('/auth/login', 'AuthController@login').name('api.auth.login').middleware('throttle:10/minute'),
    Route.post('/auth/register', 'AuthController@register').name('api.auth.register').middleware('throttle:5/minute'),
    Route.post('/auth/verify-email', 'AuthController@verify_email').name('api.auth.verify_email').middleware('throttle:10/minute'),
    Route.post('/auth/forgot-password', 'AuthController@forgot_password').name('api.auth.forgot_password').middleware('throttle:5/minute'),
    Route.post('/auth/reset-password', 'Auth<PERSON><PERSON>roll<PERSON>@reset_password').name('api.auth.reset_password').middleware('throttle:10/minute'),
    
    # Protected authentication endpoints with moderate rate limiting
    Route.get('/auth/profile', 'AuthController@profile').name('api.auth.profile').middleware('auth', 'throttle:60/minute'),
    Route.post('/auth/logout', 'AuthController@logout').name('api.auth.logout').middleware('auth', 'throttle:20/minute'),
    Route.post('/auth/refresh', 'AuthController@refresh').name('api.auth.refresh').middleware('auth', 'throttle:30/minute'),
    
    # Two-Factor Authentication endpoints with security-focused rate limiting
    Route.post('/two-factor/setup', 'TwoFactorController@setup').name('api.two_factor.setup').middleware('auth', 'throttle:5/minute'),
    Route.post('/two-factor/verify', 'TwoFactorController@verify').name('api.two_factor.verify').middleware('auth', 'throttle:10/minute'),
    Route.post('/two-factor/disable', 'TwoFactorController@disable').name('api.two_factor.disable').middleware('auth', 'throttle:5/minute'),
    Route.get('/two-factor/recovery-codes', 'TwoFactorController@recovery_codes').name('api.two_factor.recovery_codes').middleware('auth', 'throttle:10/minute'),
    Route.post('/two-factor/regenerate-codes', 'TwoFactorController@regenerate_codes').name('api.two_factor.regenerate_codes').middleware('auth', 'throttle:3/minute'),
      # OAuth Authentication endpoints with moderate rate limiting
    Route.get('/oauth/@provider/url', 'OAuthController@get_oauth_url').name('api.oauth.get_url').middleware('throttle:20/minute'),
    Route.post('/oauth/@provider/callback', 'OAuthController@handle_oauth_callback').name('api.oauth.callback').middleware('throttle:20/minute'),
    Route.get('/oauth/callback', 'OAuthController@handle_oauth_redirect').name('api.oauth.redirect'),  # No rate limit for provider redirects
    Route.post('/oauth/exchange-token', 'OAuthController@exchange_authorization_code').name('api.oauth.exchange').middleware('throttle:20/minute'),
    Route.get('/oauth/providers', 'OAuthController@get_available_providers').name('api.oauth.providers').middleware('throttle:30/minute'),
    
    # Payment endpoints with security-focused rate limiting
    Route.post('/payments/create-order', 'PaymentController@create_order').name('api.payments.create_order').middleware('auth', 'throttle:100/minute'),
    Route.post('/payments/verify', 'PaymentController@verify_payment').name('api.payments.verify').middleware('auth', 'throttle:20/minute'),
    Route.get('/payments/status/@order_id', 'PaymentController@get_payment_status').name('api.payments.status').middleware('auth', 'throttle:30/minute'),
    Route.get('/payments/user', 'PaymentController@get_user_payments').name('api.payments.user').middleware('auth', 'throttle:30/minute'),
    Route.post('/payments/refund', 'PaymentController@refund_payment').name('api.payments.refund').middleware('auth', 'throttle:5/minute'),
    Route.get('/payments/analytics', 'PaymentController@get_payment_analytics').name('api.payments.analytics').middleware('auth', 'throttle:20/minute'),
    Route.get('/payments/refunds', 'PaymentController@get_refund_history').name('api.payments.refunds').middleware('auth', 'throttle:30/minute'),
    Route.post('/payments/cancel', 'PaymentController@cancel_payment').name('api.payments.cancel').middleware('auth', 'throttle:10/minute'),
    
    # Payment webhook (unauthenticated for Razorpay callbacks)
    Route.post('/payments/webhook', 'PaymentWebhookController@handle_webhook').name('api.payments.webhook').middleware('throttle:100/minute'),
    
    # Test endpoint to verify PaymentController is working
    Route.get('/payments/test', 'PaymentController@test').name('api.payments.test'),

    # Account Management endpoints (authenticated)
    Route.post('/account/request-deletion', 'AccountController@request_deletion').name('api.account.request_deletion').middleware('auth', 'throttle:3/hour'),
    Route.get('/account/deletion-status', 'AccountController@deletion_status').name('api.account.deletion_status').middleware('auth', 'throttle:20/minute'),
    Route.post('/account/cancel-deletion', 'AccountController@cancel_deletion').name('api.account.cancel_deletion').middleware('auth', 'throttle:5/minute'),
    Route.get('/account/export-data', 'AccountController@export_data').name('api.account.export_data').middleware('auth', 'throttle:5/hour'),
    Route.post('/account/request-export', 'AccountController@request_export').name('api.account.request_export').middleware('auth', 'throttle:3/hour'),
    Route.post('/account/cleanup-expired', 'AccountController@cleanup_expired').name('api.account.cleanup_expired').middleware('throttle:10/hour'),  # Admin endpoint

    # Account Management endpoints (public - no authentication required)
    Route.post('/account/confirm-deletion', 'AccountPublicController@confirm_deletion').name('api.account.confirm_deletion').middleware('throttle:10/hour'),
    Route.post('/account/check-preserved-data', 'AccountPublicController@check_preserved_data').name('api.account.check_preserved_data').middleware('throttle:20/hour'),
    Route.post('/account/restore-data', 'AccountPublicController@restore_data').name('api.account.restore_data').middleware('throttle:5/hour'),
    Route.delete('/account/delete-preserved-data', 'AccountPublicController@delete_preserved_data').name('api.account.delete_preserved_data').middleware('throttle:3/hour'),

    # OTP endpoints (public - no authentication required)
    Route.post('/otp/send', 'OTPController@send_otp').name('api.otp.send').middleware('throttle:5/minute'),
    Route.post('/otp/send-email', 'OTPController@send_email_otp').name('api.otp.send_email').middleware('throttle:5/minute'),
    Route.post('/otp/send-sms', 'OTPController@send_sms_otp').name('api.otp.send_sms').middleware('throttle:3/minute'),
    Route.post('/otp/verify', 'OTPController@verify_otp').name('api.otp.verify').middleware('throttle:10/minute'),
    Route.post('/otp/login', 'OTPController@login_with_otp').name('api.otp.login').middleware('throttle:5/minute'),
    Route.get('/otp/status', 'OTPController@get_otp_status').name('api.otp.status').middleware('throttle:20/minute'),
    Route.post('/otp/cleanup', 'OTPController@cleanup_otps').name('api.otp.cleanup').middleware('throttle:10/hour'),  # Admin endpoint

    # Security endpoints (authentication required)
    Route.get('/security/dashboard', 'SecurityController@get_security_dashboard').name('api.security.dashboard').middleware(['auth', 'throttle:30/minute']),
    Route.get('/security/events/user', 'SecurityController@get_user_security_events').name('api.security.user_events').middleware(['auth', 'throttle:20/minute']),
    Route.get('/security/statistics', 'SecurityController@get_security_statistics').name('api.security.statistics').middleware(['auth', 'throttle:20/minute']),
    Route.get('/security/account-status', 'SecurityController@check_account_status').name('api.security.account_status').middleware(['auth', 'throttle:10/minute']),

    # Admin security endpoints (authentication required)
    Route.post('/security/unlock-account', 'SecurityController@unlock_account').name('api.security.unlock_account').middleware(['auth', 'throttle:5/hour']),
    Route.post('/security/events/{event_id}/resolve', 'SecurityController@resolve_security_event').name('api.security.resolve_event').middleware(['auth', 'throttle:20/hour']),
    Route.post('/security/cleanup', 'SecurityController@cleanup_security_data').name('api.security.cleanup').middleware(['auth', 'throttle:2/hour']),

    # Notification endpoints (authentication required)
    Route.get('/notifications', 'NotificationController@get_user_notifications').name('api.notifications.list').middleware(['auth', 'throttle:30/minute']),
    Route.post('/notifications/{notification_id}/read', 'NotificationController@mark_notification_read').name('api.notifications.mark_read').middleware(['auth', 'throttle:60/minute']),
    Route.post('/notifications/test', 'NotificationController@send_test_notification').name('api.notifications.test').middleware(['auth', 'throttle:5/hour']),

    # Queue endpoints (authentication required)
    Route.get('/queue/stats', 'QueueController@get_queue_stats').name('api.queue.stats').middleware(['auth', 'throttle:20/minute']),
    Route.post('/queue/test-email', 'QueueController@queue_test_email').name('api.queue.test_email').middleware(['auth', 'throttle:5/hour']),
    Route.post('/queue/cleanup', 'QueueController@trigger_data_cleanup').name('api.queue.cleanup').middleware(['auth', 'throttle:3/hour']),
    Route.post('/queue/process-security-events', 'QueueController@process_security_events').name('api.queue.process_security').middleware(['auth', 'throttle:10/hour']),
]
