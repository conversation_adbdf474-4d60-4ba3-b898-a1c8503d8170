#!/usr/bin/env python3
"""
Notification System Test Suite
Tests multi-channel notifications (Email, SMS, Database) including OTP notifications, security alerts, and notification delivery
"""

import requests
import json
import time
import sys
import random

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def print_header(title):
    """Print formatted test header"""
    print(f"\n{'='*60}")
    print(f"📧 {title}")
    print(f"{'='*60}")

def print_test(test_name):
    """Print test name"""
    print(f"\n🧪 Testing: {test_name}")
    print("-" * 40)

def create_test_user():
    """Create a test user and return token"""
    unique_id = random.randint(10000, 99999)
    register_data = {
        "email": f"notification_test_{unique_id}@example.com",
        "password": "SecurePass123!",
        "firstName": "Notification",
        "lastName": "Test"
    }
    
    try:
        # Wait to avoid rate limiting
        time.sleep(2)
        response = requests.post(f"{API_BASE}/auth/register", json=register_data)
        if response.status_code == 201:
            return response.json().get('token'), register_data['email']
        elif response.status_code == 429:
            print("⚠️ Rate limited - waiting 60 seconds...")
            time.sleep(60)
            response = requests.post(f"{API_BASE}/auth/register", json=register_data)
            if response.status_code == 201:
                return response.json().get('token'), register_data['email']
        return None, None
    except Exception as e:
        print(f"❌ Failed to create test user: {str(e)}")
        return None, None

def test_notification_endpoints():
    """Test notification system endpoints"""
    print_test("Notification Endpoints")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for notifications")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    results = {}
    
    # Test get notifications
    try:
        time.sleep(1)
        response = requests.get(f"{API_BASE}/notifications", headers=headers)
        results['get_notifications'] = response.status_code == 200
        print(f"Get Notifications: {response.status_code} - {'✅' if results['get_notifications'] else '❌'}")
        
        if response.status_code == 200:
            notifications_data = response.json()
            print(f"Notifications Response: {json.dumps(notifications_data, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['get_notifications'] = False
        print(f"❌ Get Notifications error: {str(e)}")
    
    # Test send test notification
    try:
        time.sleep(3)  # Longer wait for rate limiting
        response = requests.post(f"{API_BASE}/notifications/test", headers=headers)
        results['test_notification'] = response.status_code in [200, 201]
        print(f"Test Notification: {response.status_code} - {'✅' if results['test_notification'] else '❌'}")
        
        if response.status_code in [200, 201]:
            test_response = response.json()
            print(f"Test Notification Response: {json.dumps(test_response, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['test_notification'] = False
        print(f"❌ Test Notification error: {str(e)}")
    
    return any(results.values())

def test_otp_notifications():
    """Test OTP notification system"""
    print_test("OTP Notification System")
    
    results = {}
    
    # Test OTP status (public endpoint)
    try:
        response = requests.get(f"{API_BASE}/otp/status")
        results['otp_status'] = response.status_code == 200
        print(f"OTP Status: {response.status_code} - {'✅' if results['otp_status'] else '❌'}")
        
        if response.status_code == 200:
            status_data = response.json()
            print(f"OTP Status Response: {json.dumps(status_data, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['otp_status'] = False
        print(f"❌ OTP Status error: {str(e)}")
    
    # Test send OTP email
    try:
        time.sleep(3)
        otp_data = {
            "email": "<EMAIL>",
            "type": "login"
        }
        response = requests.post(f"{API_BASE}/otp/send-email", json=otp_data)
        results['send_otp_email'] = response.status_code in [200, 201]
        print(f"Send OTP Email: {response.status_code} - {'✅' if results['send_otp_email'] else '❌'}")
        
        if response.status_code in [200, 201]:
            otp_response = response.json()
            print(f"OTP Email Response: {json.dumps(otp_response, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['send_otp_email'] = False
        print(f"❌ Send OTP Email error: {str(e)}")
    
    # Test send general OTP
    try:
        time.sleep(3)
        otp_data = {
            "email": "<EMAIL>",
            "type": "verification"
        }
        response = requests.post(f"{API_BASE}/otp/send", json=otp_data)
        results['send_otp'] = response.status_code in [200, 201]
        print(f"Send OTP: {response.status_code} - {'✅' if results['send_otp'] else '❌'}")
        
        if response.status_code in [200, 201]:
            otp_response = response.json()
            print(f"OTP Response: {json.dumps(otp_response, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['send_otp'] = False
        print(f"❌ Send OTP error: {str(e)}")
    
    return any(results.values())

def test_security_notifications():
    """Test security-related notifications"""
    print_test("Security Notifications")
    
    # Create a user and trigger some security events
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for security notifications")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    results = {}
    
    # Test security dashboard (should trigger security events)
    try:
        time.sleep(2)
        response = requests.get(f"{API_BASE}/security/dashboard", headers=headers)
        results['security_dashboard'] = response.status_code == 200
        print(f"Security Dashboard: {response.status_code} - {'✅' if results['security_dashboard'] else '❌'}")
        
        if response.status_code == 200:
            dashboard_data = response.json()
            print(f"Security Dashboard: {json.dumps(dashboard_data, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['security_dashboard'] = False
        print(f"❌ Security Dashboard error: {str(e)}")
    
    # Test user security events
    try:
        time.sleep(2)
        response = requests.get(f"{API_BASE}/security/events/user", headers=headers)
        results['security_events'] = response.status_code == 200
        print(f"Security Events: {response.status_code} - {'✅' if results['security_events'] else '❌'}")
        
        if response.status_code == 200:
            events_data = response.json()
            print(f"Security Events: {json.dumps(events_data, indent=2)}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        results['security_events'] = False
        print(f"❌ Security Events error: {str(e)}")
    
    return any(results.values())

def run_notification_tests():
    """Run all notification system tests"""
    print_header("NOTIFICATION SYSTEM TEST SUITE")
    
    # Wait for any rate limiting to reset
    print("⏳ Waiting 10 seconds for rate limiting to reset...")
    time.sleep(10)
    
    tests = [
        ("Notification Endpoints", test_notification_endpoints),
        ("OTP Notifications", test_otp_notifications),
        ("Security Notifications", test_security_notifications)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
            
            # Add delay between test suites
            time.sleep(5)
        except Exception as e:
            results[test_name] = False
            print(f"\n❌ FAILED: {test_name} - {str(e)}")
    
    # Print summary
    print_header("NOTIFICATION TESTS SUMMARY")
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} test suites passed")
    
    if passed >= total * 0.67:  # 67% pass rate
        print("🎉 NOTIFICATION SYSTEM TESTS MOSTLY SUCCESSFUL!")
        return True
    else:
        print("⚠️ Notification system needs attention")
        return False

if __name__ == "__main__":
    success = run_notification_tests()
    sys.exit(0 if success else 1)
