#!/usr/bin/env python3
"""
Final CORS Test
Test CORS headers are properly set
"""

import requests
import json
import sys

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_cors_headers():
    """Test CORS headers on endpoints"""
    print("🔍 Testing CORS Headers")
    print("=" * 50)
    
    endpoints = [
        "/payments/test",
        "/oauth/providers",
        "/otp/status"
    ]
    
    for endpoint in endpoints:
        print(f"\n📍 Testing endpoint: {endpoint}")
        
        try:
            # Test GET request
            response = requests.get(f"{API_BASE}{endpoint}")
            print(f"   GET Status: {response.status_code}")
            
            # Check for CORS headers
            cors_headers = {
                "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
                "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers"),
                "Access-Control-Allow-Credentials": response.headers.get("Access-Control-Allow-Credentials"),
                "Access-Control-Max-Age": response.headers.get("Access-Control-Max-Age"),
                "Access-Control-Expose-Headers": response.headers.get("Access-Control-Expose-Headers")
            }
            
            print(f"   CORS Headers:")
            for header, value in cors_headers.items():
                if value:
                    print(f"     ✅ {header}: {value}")
                else:
                    print(f"     ❌ {header}: Not set")
            
            # Test OPTIONS preflight request
            print(f"\n   Testing OPTIONS preflight:")
            options_response = requests.options(f"{API_BASE}{endpoint}")
            print(f"   OPTIONS Status: {options_response.status_code}")
            
            options_cors_headers = {
                "Access-Control-Allow-Origin": options_response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Methods": options_response.headers.get("Access-Control-Allow-Methods"),
                "Access-Control-Allow-Headers": options_response.headers.get("Access-Control-Allow-Headers"),
                "Access-Control-Allow-Credentials": options_response.headers.get("Access-Control-Allow-Credentials"),
                "Access-Control-Max-Age": options_response.headers.get("Access-Control-Max-Age")
            }
            
            print(f"   OPTIONS CORS Headers:")
            for header, value in options_cors_headers.items():
                if value:
                    print(f"     ✅ {header}: {value}")
                else:
                    print(f"     ❌ {header}: Not set")
            
        except Exception as e:
            print(f"   ❌ Error testing {endpoint}: {str(e)}")

def test_cors_with_origin():
    """Test CORS with specific origin header"""
    print("\n🌐 Testing CORS with Origin Header")
    print("=" * 50)
    
    headers = {
        "Origin": "http://localhost:3000",
        "Access-Control-Request-Method": "POST",
        "Access-Control-Request-Headers": "Content-Type,Authorization"
    }
    
    try:
        # Test preflight request with origin
        response = requests.options(f"{API_BASE}/auth/login", headers=headers)
        print(f"Preflight Status: {response.status_code}")
        
        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get("Access-Control-Allow-Origin"),
            "Access-Control-Allow-Methods": response.headers.get("Access-Control-Allow-Methods"),
            "Access-Control-Allow-Headers": response.headers.get("Access-Control-Allow-Headers"),
            "Access-Control-Allow-Credentials": response.headers.get("Access-Control-Allow-Credentials")
        }
        
        print("CORS Response Headers:")
        for header, value in cors_headers.items():
            if value:
                print(f"  ✅ {header}: {value}")
            else:
                print(f"  ❌ {header}: Not set")
        
        # Test actual request with origin
        get_response = requests.get(f"{API_BASE}/payments/test", headers={"Origin": "http://localhost:3000"})
        print(f"\nActual Request Status: {get_response.status_code}")
        
        actual_cors_headers = {
            "Access-Control-Allow-Origin": get_response.headers.get("Access-Control-Allow-Origin"),
            "Access-Control-Allow-Credentials": get_response.headers.get("Access-Control-Allow-Credentials")
        }
        
        print("Actual Request CORS Headers:")
        for header, value in actual_cors_headers.items():
            if value:
                print(f"  ✅ {header}: {value}")
            else:
                print(f"  ❌ {header}: Not set")
        
    except Exception as e:
        print(f"❌ Error testing CORS with origin: {str(e)}")

def main():
    """Run CORS tests"""
    print("🔒 CORS CONFIGURATION TEST")
    print("=" * 60)
    
    test_cors_headers()
    test_cors_with_origin()
    
    print("\n" + "=" * 60)
    print("🎯 CORS Test Complete")

if __name__ == "__main__":
    main()
