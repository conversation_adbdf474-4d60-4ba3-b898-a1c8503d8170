#!/usr/bin/env python3
"""
Focused Security Features Test
Tests the actual implemented security, notification, and queue endpoints
"""

import requests
import json
import time
import sys
import random
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def print_header(title):
    """Print formatted test header"""
    print(f"\n{'='*60}")
    print(f"🔒 {title}")
    print(f"{'='*60}")

def print_test(test_name):
    """Print test name"""
    print(f"\n🧪 Testing: {test_name}")
    print("-" * 40)

def create_test_user():
    """Create a test user and return token"""
    unique_id = random.randint(10000, 99999)
    register_data = {
        "email": f"test_user_{unique_id}@example.com",
        "password": "SecurePass123!",
        "firstName": "Test",
        "lastName": "User"
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/register", json=register_data)
        if response.status_code == 201:
            return response.json().get('token'), register_data['email']
        elif response.status_code == 429:
            print("⚠️ Rate limited - waiting 30 seconds...")
            time.sleep(30)
            response = requests.post(f"{API_BASE}/auth/register", json=register_data)
            if response.status_code == 201:
                return response.json().get('token'), register_data['email']
        return None, None
    except Exception as e:
        print(f"❌ Failed to create test user: {str(e)}")
        return None, None

def test_security_endpoints():
    """Test security monitoring endpoints"""
    print_test("Security Endpoints")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    results = {}
    
    # Test security dashboard
    try:
        time.sleep(1)
        response = requests.get(f"{API_BASE}/security/dashboard", headers=headers)
        results['dashboard'] = response.status_code == 200
        print(f"Security Dashboard: {response.status_code} - {'✅' if results['dashboard'] else '❌'}")
        if response.status_code == 200:
            print(f"Dashboard data: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['dashboard'] = False
        print(f"❌ Security Dashboard error: {str(e)}")
    
    # Test user security events
    try:
        time.sleep(1)
        response = requests.get(f"{API_BASE}/security/events/user", headers=headers)
        results['events'] = response.status_code == 200
        print(f"Security Events: {response.status_code} - {'✅' if results['events'] else '❌'}")
        if response.status_code == 200:
            print(f"Events data: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['events'] = False
        print(f"❌ Security Events error: {str(e)}")
    
    # Test account status
    try:
        time.sleep(1)
        response = requests.get(f"{API_BASE}/security/account-status", headers=headers)
        results['account_status'] = response.status_code == 200
        print(f"Account Status: {response.status_code} - {'✅' if results['account_status'] else '❌'}")
        if response.status_code == 200:
            print(f"Account status: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['account_status'] = False
        print(f"❌ Account Status error: {str(e)}")
    
    return any(results.values())

def test_otp_endpoints():
    """Test OTP system endpoints"""
    print_test("OTP System Endpoints")
    
    results = {}
    
    # Test OTP status (public endpoint)
    try:
        response = requests.get(f"{API_BASE}/otp/status")
        results['otp_status'] = response.status_code == 200
        print(f"OTP Status: {response.status_code} - {'✅' if results['otp_status'] else '❌'}")
        if response.status_code == 200:
            print(f"OTP Status data: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['otp_status'] = False
        print(f"❌ OTP Status error: {str(e)}")
    
    # Test send OTP (with a test email)
    try:
        time.sleep(2)
        otp_data = {
            "email": "<EMAIL>",
            "type": "login"
        }
        response = requests.post(f"{API_BASE}/otp/send", json=otp_data)
        results['send_otp'] = response.status_code in [200, 201]
        print(f"Send OTP: {response.status_code} - {'✅' if results['send_otp'] else '❌'}")
        if response.status_code in [200, 201]:
            print(f"Send OTP response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['send_otp'] = False
        print(f"❌ Send OTP error: {str(e)}")
    
    return any(results.values())

def test_notification_endpoints():
    """Test notification system endpoints"""
    print_test("Notification System Endpoints")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for notifications")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    results = {}
    
    # Test get notifications
    try:
        time.sleep(1)
        response = requests.get(f"{API_BASE}/notifications", headers=headers)
        results['get_notifications'] = response.status_code == 200
        print(f"Get Notifications: {response.status_code} - {'✅' if results['get_notifications'] else '❌'}")
        if response.status_code == 200:
            print(f"Notifications: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['get_notifications'] = False
        print(f"❌ Get Notifications error: {str(e)}")
    
    # Test send test notification
    try:
        time.sleep(2)
        response = requests.post(f"{API_BASE}/notifications/test", headers=headers)
        results['test_notification'] = response.status_code in [200, 201]
        print(f"Test Notification: {response.status_code} - {'✅' if results['test_notification'] else '❌'}")
        if response.status_code in [200, 201]:
            print(f"Test notification response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['test_notification'] = False
        print(f"❌ Test Notification error: {str(e)}")
    
    return any(results.values())

def test_queue_endpoints():
    """Test queue system endpoints"""
    print_test("Queue System Endpoints")
    
    token, email = create_test_user()
    if not token:
        print("❌ Failed to create test user for queue")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    results = {}
    
    # Test queue stats
    try:
        time.sleep(1)
        response = requests.get(f"{API_BASE}/queue/stats", headers=headers)
        results['queue_stats'] = response.status_code == 200
        print(f"Queue Stats: {response.status_code} - {'✅' if results['queue_stats'] else '❌'}")
        if response.status_code == 200:
            print(f"Queue stats: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['queue_stats'] = False
        print(f"❌ Queue Stats error: {str(e)}")
    
    # Test queue test email
    try:
        time.sleep(2)
        response = requests.post(f"{API_BASE}/queue/test-email", headers=headers)
        results['test_email'] = response.status_code in [200, 201]
        print(f"Queue Test Email: {response.status_code} - {'✅' if results['test_email'] else '❌'}")
        if response.status_code in [200, 201]:
            print(f"Test email response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        results['test_email'] = False
        print(f"❌ Queue Test Email error: {str(e)}")
    
    return any(results.values())

def run_focused_tests():
    """Run focused tests on implemented features"""
    print_header("FOCUSED SECURITY FEATURES TEST")
    
    # Wait for any rate limiting to reset
    print("⏳ Waiting 10 seconds for rate limiting to reset...")
    time.sleep(10)
    
    tests = [
        ("Security Endpoints", test_security_endpoints),
        ("OTP System", test_otp_endpoints),
        ("Notification System", test_notification_endpoints),
        ("Queue System", test_queue_endpoints)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
            
            # Add delay between test suites
            time.sleep(3)
        except Exception as e:
            results[test_name] = False
            print(f"\n❌ FAILED: {test_name} - {str(e)}")
    
    # Print summary
    print_header("FOCUSED TESTS SUMMARY")
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} test suites passed")
    
    if passed >= total * 0.75:  # 75% pass rate
        print("🎉 ADVANCED FEATURES TESTS MOSTLY SUCCESSFUL!")
        return True
    else:
        print("⚠️ Some advanced features need attention")
        return False

if __name__ == "__main__":
    success = run_focused_tests()
    sys.exit(0 if success else 1)
