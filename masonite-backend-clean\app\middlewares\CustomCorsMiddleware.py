"""Custom CORS Middleware for handling Cross-Origin Resource Sharing"""

from masonite.middleware import Middleware
from masonite.request import Request
from masonite.response import Response


class CustomCorsMiddleware(Middleware):
    """Custom CORS middleware that actually works"""

    def before(self, request: Request, response: Response):
        """Handle CORS headers before request processing"""

        print(f"🔍 CORS Middleware BEFORE: {request.get_request_method()} {request.get_path()}")

        # Set CORS headers on every request
        self._set_cors_headers(response)

        # Handle preflight OPTIONS requests
        if request.get_request_method().upper() == 'OPTIONS':
            print("🔍 CORS: Handling OPTIONS preflight request")
            return response.json({}, 204)

        return request

    def after(self, request: Request, response: Response):
        """Ensure CORS headers are set after request processing"""

        print(f"🔍 CORS Middleware AFTER: {request.get_request_method()} {request.get_path()}")

        # Always set CORS headers on response
        self._set_cors_headers(response)

        return request

    def _set_cors_headers(self, response: Response):
        """Set CORS headers on response"""
        print("🔍 CORS: Setting headers on response")
        response.header('Access-Control-Allow-Origin', '*')
        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Max-Age', '86400')
        response.header('Access-Control-Expose-Headers', 'X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset')
        print("🔍 CORS: Headers set successfully")
